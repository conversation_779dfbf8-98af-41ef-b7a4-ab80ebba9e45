.chat-bubble {
  max-width: 80%;
  margin-bottom: 16px;
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  min-width: 0; /* Allow flex item to shrink */
}

.chat-bubble.user {
  align-self: flex-end;
  background-color: #007acc;
  color: white;
  margin-left: auto;
}

/* Text selection styling for user messages */
.chat-bubble.user ::selection {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.chat-bubble.user ::-moz-selection {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.chat-bubble.assistant {
  align-self: flex-start;
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
}

/* Text selection styling for assistant messages */
.chat-bubble.assistant ::selection {
  background-color: #007acc;
  color: white;
}

.chat-bubble.assistant ::-moz-selection {
  background-color: #007acc;
  color: white;
}

.chat-bubble.error {
  align-self: flex-start;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.bubble-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  opacity: 0.8;
}

.sender {
  font-weight: 600;
  margin-right: 8px;
}

.timestamp {
  font-size: 11px;
}

.streaming-indicator {
  color: #28a745;
  animation: pulse 1.5s infinite;
  margin-left: 8px;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.bubble-content {
  line-height: 1.5;
}

.content-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
  overflow: hidden;
}

.content-line {
  margin: 0;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.bubble-content p {
  margin: 0 0 8px 0;
}

.bubble-content p:last-child {
  margin-bottom: 0;
}

.bubble-content ol,
.bubble-content ul {
  margin: 8px 0;
  padding-left: 20px; /* Reduced since we're using inside positioning */
  margin-left: 0; /* Reset any inherited margin */
  list-style-position: inside; /* Position numbers inside the content area */
}

.bubble-content li {
  margin: 4px 0;
  padding-left: 0; /* Reset any inherited padding */
}

.bubble-content code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.chat-bubble.user .bubble-content code {
  background-color: rgba(255, 255, 255, 0.2);
}

.bubble-content pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  overflow-y: hidden;
  margin: 8px 0;
  white-space: pre;
  word-wrap: normal;
  overflow-wrap: normal;
  max-width: 100%;
  box-sizing: border-box;
}

.chat-bubble.user .bubble-content pre {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.error-content {
  font-family: monospace;
  white-space: pre-wrap;
}

.message-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 200px;
}

.image-preview img {
  max-width: 100%;
  max-height: 150px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #e9ecef;
}

.image-name {
  font-size: 11px;
  margin-top: 4px;
  text-align: center;
  opacity: 0.8;
  word-break: break-all;
}

/* Syntax highlighting adjustments */
.chat-bubble pre[class*="language-"] {
  margin: 8px 0;
  border-radius: 6px;
}

.chat-bubble code[class*="language-"] {
  font-size: 13px;
}

/* Markdown content styles */
.markdown-content {
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.markdown-content p {
  margin: 0 0 8px 0;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child,
.markdown-content h4:first-child,
.markdown-content h5:first-child,
.markdown-content h6:first-child {
  margin-top: 0;
}

.markdown-content blockquote {
  border-left: 4px solid #ddd;
  padding-left: 16px;
  margin: 8px 0;
  color: #666;
}

.chat-bubble.user .markdown-content blockquote {
  border-left-color: rgba(255, 255, 255, 0.5);
  color: rgba(255, 255, 255, 0.9);
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #ddd;
  padding: 6px 12px;
  text-align: left;
}

.markdown-content th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.chat-bubble.user .markdown-content th {
  background-color: rgba(255, 255, 255, 0.1);
}

.chat-bubble.user .markdown-content th,
.chat-bubble.user .markdown-content td {
  border-color: rgba(255, 255, 255, 0.3);
}

/* KaTeX math styling */
.katex {
  font-size: 1em !important;
}

.katex-display {
  margin: 12px 0 !important;
  text-align: center;
}

/* Underline support */
.markdown-content u {
  text-decoration: underline;
}

/* Thinking Section Styles */
.thinking-section {
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.08), rgba(148, 163, 184, 0.12));
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  margin-bottom: 16px;
  overflow: hidden;
  animation: fadeIn 0.4s ease-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
}

.chat-bubble.user .thinking-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.18));
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 14px;
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.12), rgba(148, 163, 184, 0.18));
  border-bottom: 1px solid rgba(148, 163, 184, 0.25);
  font-size: 13px;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Inter", sans-serif;
  letter-spacing: 0.01em;
}

.chat-bubble.user .thinking-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.25));
  border-bottom-color: rgba(255, 255, 255, 0.3);
}

.thinking-icon {
  font-size: 16px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.thinking-title {
  color: #64748b;
  flex-grow: 1;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.chat-bubble.user .thinking-title {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.thinking-count {
  color: #64748b;
  font-size: 11px;
  opacity: 0.85;
  font-weight: 500;
  background-color: rgba(148, 163, 184, 0.15);
  padding: 2px 6px;
  border-radius: 8px;
}

.chat-bubble.user .thinking-count {
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(255, 255, 255, 0.2);
}

.thinking-steps {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(108, 117, 125, 0.3) transparent;
}

.thinking-steps::-webkit-scrollbar {
  width: 6px;
}

.thinking-steps::-webkit-scrollbar-track {
  background: transparent;
}

.thinking-steps::-webkit-scrollbar-thumb {
  background-color: rgba(108, 117, 125, 0.3);
  border-radius: 3px;
}

.thinking-step {
  border-bottom: 1px solid rgba(108, 117, 125, 0.1);
  animation: slideIn 0.2s ease-out;
}

.thinking-step:last-child {
  border-bottom: none;
}

.chat-bubble.user .thinking-step {
  border-bottom-color: rgba(255, 255, 255, 0.15);
}

.thinking-step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 14px 6px 14px;
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.06), rgba(148, 163, 184, 0.1));
  border-left: 3px solid rgba(59, 130, 246, 0.4);
}

.chat-bubble.user .thinking-step-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.15));
  border-left-color: rgba(255, 255, 255, 0.5);
}

.thinking-step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  font-size: 9px;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Inter", sans-serif;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.chat-bubble.user .thinking-step-number {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  color: #007acc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.thinking-step-time {
  font-size: 10px;
  color: #64748b;
  opacity: 0.9;
  font-weight: 500;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Consolas", "Liberation Mono", monospace;
  background-color: rgba(148, 163, 184, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.chat-bubble.user .thinking-step-time {
  color: rgba(255, 255, 255, 0.85);
  background-color: rgba(255, 255, 255, 0.15);
}

.thinking-step-content {
  padding: 10px 14px 14px 14px;
  line-height: 1.5;
  border-left: 3px solid rgba(148, 163, 184, 0.15);
  margin-left: 9px;
}

.chat-bubble.user .thinking-step-content {
  border-left-color: rgba(255, 255, 255, 0.2);
}

.thinking-markdown {
  font-size: 13px;
  line-height: 1.5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Inter", sans-serif;
  color: #475569;
}

.chat-bubble.user .thinking-markdown {
  color: rgba(255, 255, 255, 0.9);
}

.thinking-markdown p {
  margin: 0 0 8px 0;
}

.thinking-markdown p:last-child {
  margin-bottom: 0;
}

.thinking-markdown code {
  background-color: rgba(148, 163, 184, 0.15);
  padding: 2px 5px;
  border-radius: 4px;
  font-size: 12px;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Consolas", "Liberation Mono", monospace;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.chat-bubble.user .thinking-markdown code {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-15px) scale(0.99);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-bubble {
    max-width: 95%;
  }
  
  .image-preview {
    max-width: 150px;
  }
  
  .image-preview img {
    max-height: 100px;
  }

  .thinking-steps {
    max-height: 200px;
  }

  .thinking-header {
    font-size: 12px;
    padding: 6px 10px;
  }

  .thinking-step-content {
    padding: 6px 10px 10px 10px;
  }

  .thinking-markdown {
    font-size: 12px;
  }
} 