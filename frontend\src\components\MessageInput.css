.message-input-container {
  border-top: 1px solid #e9ecef;
  background-color: #ffffff;
  padding: 16px 20px;
}

.selected-files {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.file-chip {
  display: flex;
  align-items: center;
  background-color: #e9ecef;
  border-radius: 16px;
  padding: 4px 8px 4px 12px;
  font-size: 12px;
  color: #495057;
  transition: all 0.2s ease;
}

.file-chip.screenshot-chip {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.screenshot-indicator {
  margin-right: 4px;
  font-size: 14px;
}

.file-name {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 16px;
  margin-left: 6px;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-file:hover {
  background-color: #dc3545;
  color: white;
}

.file-chip.screenshot-chip .remove-file {
  color: #155724;
}

.file-chip.screenshot-chip .remove-file:hover {
  background-color: #dc3545;
  color: white;
}

.message-form {
  width: 100%;
}

.input-row {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 8px;
  transition: border-color 0.2s ease;
}

.input-row:focus-within {
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.attach-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.attach-button:hover:not(:disabled) {
  background-color: #e9ecef;
}

.attach-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.message-textarea {
  flex: 1;
  border: none;
  background: transparent;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  padding: 8px 12px;
  outline: none;
  min-height: 20px;
  max-height: 120px;
  overflow-y: auto;
}

.message-textarea::placeholder {
  color: #6c757d;
}

.message-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  background-color: #007acc;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  min-width: 40px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover:not(:disabled) {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.send-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .message-input-container {
    padding: 12px 16px;
  }
  
  .file-name {
    max-width: 100px;
  }
  
  .message-textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }
} 