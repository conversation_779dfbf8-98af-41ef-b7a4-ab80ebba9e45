/* Backend Loading Modal Styles */
.backend-loading-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.backend-loading-modal {
  background: linear-gradient(135deg, #ffffff 0%, #fcfcfc 100%);
  border-radius: 20px;
  padding: 32px;
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideIn 0.3s ease-out;
  position: relative;
}

@keyframes slideIn {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
}

.spinner-ring {
  width: 80px;
  height: 80px;
  border: 4px solid #f8f9fa;
  border-top: 4px solid #87ceeb;
  border-radius: 50%;
}

/* Loading Content */
.loading-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-content h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #2c3e50;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  white-space: nowrap;
}

/* Retry Section */
.retry-section {
  margin-top: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #dee2e6;
  animation: slideInUp 0.3s ease-out;
  align-self: stretch;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.retry-text {
  font-size: 14px;
  color: #6c757d;
  margin: 0 0 12px 0;
  text-align: center;
}

.retry-button {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.retry-button:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
}

.retry-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .backend-loading-modal {
    max-width: 320px;
    padding: 24px;
  }
  
  .loading-content h3 {
    font-size: 20px;
  }
  
  .spinner-ring {
    width: 60px;
    height: 60px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .backend-loading-modal {
    background: linear-gradient(135deg, #3a4a5c 0%, #4a5568 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.15);
  }
  
  .loading-content h3 {
    color: #ecf0f1;
  }
  
  .retry-section {
    background-color: #34495e;
    border-color: #4a5f7a;
  }
  
  .retry-text {
    color: #bdc3c7;
  }
} 