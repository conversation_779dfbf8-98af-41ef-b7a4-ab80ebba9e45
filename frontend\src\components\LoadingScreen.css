.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.loading-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 90%;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-logo h1 {
  font-size: 3rem;
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-weight: 700;
}

.loading-logo .logo-container {
  flex-direction: column;
  margin-bottom: 0.5rem;
}

.loading-logo .logo-container .logo-text {
  margin-left: 0 !important;
  margin-top: 8px;
}

.loading-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  font-weight: 400;
}

.loading-content {
  margin: 2rem 0;
}

.loading-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.loading-message h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.loading-progress {
  margin: 1.5rem 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 4px;
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.progress-text {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 500;
}

.loading-steps {
  text-align: left;
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 10px;
  border-left: 4px solid #3498db;
}

.step {
  margin: 0.5rem 0;
  color: #2c3e50;
  font-size: 0.95rem;
  opacity: 0.8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.step.completed {
  background: rgba(39, 174, 96, 0.1);
  border-left: 3px solid #27ae60;
  opacity: 1;
  color: #27ae60;
}

.step.active {
  background: rgba(52, 152, 219, 0.15);
  border-left: 3px solid #3498db;
  opacity: 1;
  color: #3498db;
  animation: activeStep 1.5s infinite;
}

.step.pending {
  opacity: 0.5;
  color: #95a5a6;
}

@keyframes activeStep {
  0%, 100% {
    background: rgba(52, 152, 219, 0.15);
  }
  50% {
    background: rgba(52, 152, 219, 0.25);
  }
}

.step-check {
  font-size: 0.8rem;
  color: #27ae60;
}

.step-spinner {
  font-size: 0.8rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  color: #e74c3c;
  margin: 1rem 0;
  font-weight: 500;
}

.error-details {
  margin: 1rem 0;
  text-align: left;
}

.error-details summary {
  cursor: pointer;
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.error-details pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.8rem;
  color: #e74c3c;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 150px;
  overflow-y: auto;
}

.retry-button {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem 0;
}

.retry-button:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.error-help {
  background: rgba(231, 76, 60, 0.1);
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  border-left: 4px solid #e74c3c;
}

.error-help p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #2c3e50;
}

.loading-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #ecf0f1;
}

.loading-footer p {
  margin: 0.5rem 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.loading-tip {
  background: rgba(241, 196, 15, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
  border-left: 4px solid #f1c40f;
  margin: 1rem 0 0 0;
}

.loading-tip strong {
  color: #f39c12;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .loading-container {
    background: rgba(44, 62, 80, 0.95);
    color: #ecf0f1;
  }
  
  .loading-logo h1 {
    color: #ecf0f1;
  }
  
  .loading-logo .logo-container {
    filter: brightness(1.2);
  }
  
  .loading-logo .logo-text {
    color: #ecf0f1 !important;
  }
  
  .loading-message h3 {
    color: #ecf0f1;
  }
  
  .step {
    color: #bdc3c7;
  }
  
  .error-help p {
    color: #bdc3c7;
  }
  
  .loading-footer p {
    color: #95a5a6;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .loading-container {
    padding: 2rem;
    margin: 1rem;
  }
  
  .loading-logo h1 {
    font-size: 2.5rem;
  }
  
  .loading-logo .logo-container {
    margin-bottom: 0.5rem;
  }
  
  .loading-logo .logo-text {
    font-size: 2.5rem !important;
  }
  
  .loading-icon {
    font-size: 3rem;
  }
  
  .loading-message h3 {
    font-size: 1.3rem;
  }
} 