.settings-panel {
  height: 100%;
  overflow-y: auto;
  background-color: #ffffff;
}

.settings-header {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.settings-header h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.settings-header p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.settings-content {
  padding: 24px;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #007acc;
  padding-bottom: 8px;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.setting-select,
.setting-input {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.1);
}

.model-select-container {
  display: flex;
  gap: 8px;
  align-items: center;
  max-width: 300px;
}

.model-select-container .setting-select {
  flex: 1;
  max-width: none;
}

.add-model-button {
  background-color: #007acc;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
}

.add-model-button:hover:not(:disabled) {
  background-color: #0056a3;
}

.add-model-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.setting-description {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

.about-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.about-info p {
  margin: 0 0 8px 0;
  color: #495057;
}

.about-info p:last-of-type {
  margin-bottom: 16px;
}

.about-links {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.link-button {
  background-color: #007acc;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.link-button:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

/* Persona Container Styles */
.persona-container {
  margin-top: 20px;
}

/* Persona Display Mode Styles */
.persona-display-mode {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 8px;
}

.persona-display-text {
  flex: 1;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  min-height: 80px;
  max-height: 120px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.edit-persona-btn {
  background: linear-gradient(135deg, #007acc, #0056b3);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 122, 204, 0.2);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
}

.edit-persona-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056b3, #004494);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 122, 204, 0.3);
}

.edit-persona-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Persona Edit Mode Styles */
.persona-edit-mode {
  margin-top: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
}

.persona-template-selector {
  margin-bottom: 20px;
}

.persona-template-selector label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.persona-text-editor {
  margin-bottom: 20px;
}

.persona-text-editor label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.persona-textarea {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  background-color: #ffffff;
  color: #374151;
  resize: vertical;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.persona-textarea:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
}

.persona-textarea::placeholder {
  color: #6b7280;
  font-style: italic;
}

.persona-edit-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.save-persona-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
  display: flex;
  align-items: center;
  gap: 6px;
}

.save-persona-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.save-persona-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancel-persona-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-persona-btn:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

.cancel-persona-btn:disabled {
  background: #adb5bd;
  cursor: not-allowed;
  transform: none;
}

.update-message {
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.update-message.success {
  background-color: #ecfdf5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.update-message.error {
  background-color: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.update-message.info {
  background-color: #eff6ff;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

/* Disabled states */
.setting-select:disabled,
.persona-textarea:disabled {
  background-color: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Dark mode support for persona interface */
@media (prefers-color-scheme: dark) {
  .persona-display-text {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .persona-edit-mode {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .persona-textarea {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .persona-textarea:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.2);
  }
  
  .persona-textarea::placeholder {
    color: #9ca3af;
  }
  
  .persona-template-selector label,
  .persona-text-editor label {
    color: #f9fafb;
  }
  
  .update-message.success {
    background-color: #064e3b;
    color: #a7f3d0;
    border-color: #065f46;
  }
  
  .update-message.error {
    background-color: #7f1d1d;
    color: #fecaca;
    border-color: #991b1b;
  }
  
  .update-message.info {
    background-color: #1e3a8a;
    color: #93c5fd;
    border-color: #1e40af;
  }
  
  .setting-select:disabled,
  .persona-textarea:disabled {
    background-color: #374151;
    color: #6b7280;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .settings-content {
    padding: 16px;
  }
  
  .settings-header {
    padding: 16px;
  }
  
  .setting-select,
  .setting-input {
    max-width: 100%;
  }
  
  .about-links {
    flex-direction: column;
  }
  
  .link-button {
    justify-content: center;
  }
  
  /* Persona responsive styles */
  .persona-display-mode {
    flex-direction: column;
    gap: 12px;
  }
  
  .persona-display-text {
    min-height: 60px;
    max-height: 100px;
  }
  
  .edit-persona-btn {
    align-self: flex-start;
  }
  
  .persona-edit-mode {
    padding: 16px;
  }
  
  .persona-edit-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .save-persona-btn,
  .cancel-persona-btn {
    justify-content: center;
  }
}

/* API Key Management Styles */
.api-key-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.api-key-check-btn {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-key-check-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
}

.api-key-check-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.api-key-update-btn {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-key-update-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
}

.api-key-update-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Dark mode support for API key styles */
@media (prefers-color-scheme: dark) {
  .api-key-check-btn {
    background: linear-gradient(135deg, #065f46, #047857);
  }
  
  .api-key-check-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #047857, #059669);
  }
  
  .api-key-update-btn {
    background: linear-gradient(135deg, #065f46, #047857);
  }
  
  .api-key-update-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #047857, #059669);
  }
} 